# Build outputs
**/build/
**/target/
**/.gradle/

# IDE files
**/.idea/
**/.vscode/
**/*.iml
**/*.ipr
**/*.iws

# OS files
**/.DS_Store
**/Thumbs.db

# Git
**/.git/
**/.gitignore

# Logs
**/*.log
**/logs/

# Temporary files
**/tmp/
**/temp/

# Node modules (if any)
**/node_modules/

# Docker files in other services (avoid recursive copying)
**/Dockerfile
**/docker-compose.yml
**/.dockerignore

# Test files and reports
**/test-results/
**/coverage/
**/reports/

# Documentation
**/*.md
**/docs/

# Environment files
**/.env
**/.env.local
**/.env.*.local

# Database files
**/*.db
**/*.sqlite

# Backup files
**/*.bak
**/*.backup
