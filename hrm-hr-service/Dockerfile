# Stage 1: Build stage
FROM eclipse-temurin:21-jdk AS build

WORKDIR /app

# Copy Gradle wrapper and build files first for better caching
COPY gradlew gradlew.bat ./
COPY gradle/ gradle/
COPY settings.gradle ./
COPY build.gradle ./

# Copy hrm-common module (dependency)
COPY hrm-common/ hrm-common/

# Copy hrm-hr-service build files
COPY hrm-hr-service/build.gradle hrm-hr-service/

# Make gradlew executable
RUN chmod +x ./gradlew

# Download dependencies (this layer will be cached if dependencies don't change)
RUN ./gradlew hrm-hr-service:dependencies --no-daemon

# Copy source code
COPY hrm-hr-service/src/ hrm-hr-service/src/

# Build the application
RUN ./gradlew hrm-hr-service:build -x test --no-daemon

# Stage 2: Runtime stage
FROM eclipse-temurin:21-jre-jammy

WORKDIR /app

# Create non-root user for security
RUN groupadd -r hrm && useradd -r -g hrm hrm

# Copy the built JAR from build stage
COPY --from=build /app/hrm-hr-service/build/libs/*.jar app.jar

# Change ownership to non-root user
RUN chown hrm:hrm app.jar

# Switch to non-root user
USER hrm

# Expose the port
EXPOSE 6020

# Install curl for health check
USER root
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*
USER hrm

# Health check - using a simple HTTP request to test endpoint
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:6020/test || exit 1

# Run the application
ENTRYPOINT ["java", "-jar", "app.jar"]
