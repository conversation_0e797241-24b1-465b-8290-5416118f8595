# HRM HR Service - Docker Setup

This document explains how to build and run the HRM HR Service using Docker.

## Prerequisites

- Docker installed on your system
- Docker Compose (usually comes with Docker Desktop)

## Project Structure

The HRM HR Service is part of a multi-module Gradle project and depends on the `hrm-common` module. The Dockerfile is designed to handle this multi-module setup correctly.

## Building the Docker Image

### Option 1: Using Build Scripts

From the **project root directory** (where `settings.gradle` is located):

**Linux/Mac:**
```bash
chmod +x hrm-hr-service/build-docker.sh
./hrm-hr-service/build-docker.sh
```

**Windows:**
```cmd
hrm-hr-service\build-docker.bat
```

### Option 2: Manual Docker Build

From the **project root directory**:
```bash
docker build -f hrm-hr-service/Dockerfile -t hrm-hr-service:latest .
```

## Running the Service

### Option 1: Using Docker Compose (Recommended)

This will start the HR service along with PostgreSQL and Kafka:

```bash
cd hrm-hr-service
docker-compose up -d
```

To stop:
```bash
docker-compose down
```

To stop and remove volumes:
```bash
docker-compose down -v
```

### Option 2: Running Container Manually

```bash
docker run -p 6020:6020 --name hrm-hr-service hrm-hr-service:latest
```

## Configuration

### Environment Variables

The service can be configured using environment variables:

- `SPRING_DATASOURCE_URL`: Database URL (default: ***********************************)
- `SPRING_DATASOURCE_USERNAME`: Database username (default: postgres)
- `SPRING_DATASOURCE_PASSWORD`: Database password (default: ggmacket123)
- `SPRING_KAFKA_BOOTSTRAP_SERVERS`: Kafka servers (default: localhost:9092)
- `JWT_SECRET`: JWT secret key
- `JWT_EXPIRATION`: JWT expiration time in milliseconds
- `SPRING_PROFILES_ACTIVE`: Active Spring profiles

### Docker Compose Configuration

The `docker-compose.yml` file includes:
- **hrm-hr-service**: The main application service
- **postgres**: PostgreSQL database
- **kafka**: Apache Kafka for messaging
- **zookeeper**: Required for Kafka

## Accessing the Service

Once running, the service will be available at:
- **Application**: http://localhost:6020
- **Test endpoint**: http://localhost:6020/test
- **Swagger UI**: http://localhost:6020/swagger-ui.html (if configured)

## Health Check

The Docker container includes a health check that verifies the service is responding on the `/test` endpoint.

## Troubleshooting

### Build Issues

1. **"settings.gradle not found"**: Make sure you're running the build from the project root directory
2. **"hrm-common not found"**: Ensure the `hrm-common` module exists and is properly configured
3. **Java version mismatch**: The Dockerfile uses Java 21 to match the build.gradle configuration

### Runtime Issues

1. **Database connection**: Ensure PostgreSQL is running and accessible
2. **Kafka connection**: Ensure Kafka and Zookeeper are running
3. **Port conflicts**: Make sure port 6020 is not already in use

### Logs

To view container logs:
```bash
docker logs hrm-hr-service
```

For docker-compose:
```bash
docker-compose logs hrm-hr-service
```

## Development

For development, you can mount the source code as a volume to enable hot reloading:

```yaml
volumes:
  - ../hrm-hr-service/src:/app/hrm-hr-service/src
```

## Security

The Docker image runs as a non-root user (`hrm`) for security purposes.

## Multi-Stage Build

The Dockerfile uses a multi-stage build:
1. **Build stage**: Uses JDK to compile the application
2. **Runtime stage**: Uses JRE for a smaller final image

This approach reduces the final image size and improves security by not including build tools in the runtime image.
