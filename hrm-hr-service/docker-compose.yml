version: '3.8'

services:
  hrm-hr-service:
    build:
      context: ..
      dockerfile: hrm-hr-service/Dockerfile
    ports:
      - "6020:6020"
    environment:
      # Database Configuration
      - SPRING_DATASOURCE_URL=**********************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=ggmacket123
      
      # Kafka Configuration
      - SPRING_KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      
      # JWT Configuration (should match hrm-common settings)
      - JWT_SECRET=mySecretKey123456789012345678901234567890
      - JWT_EXPIRATION=86400000
      
      # Profile
      - SPRING_PROFILES_ACTIVE=docker
    depends_on:
      - postgres
      - kafka
    networks:
      - hrm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6020/test"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=hr
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=ggmacket123
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - hrm-network
    restart: unless-stopped

  kafka:
    image: bitnami/kafka:latest
    container_name: kafka
    ports:
      - "9092:9092"
    environment:
      - KAFKA_CFG_NODE_ID=0
      - KAFKA_CFG_PROCESS_ROLES=controller,broker
      - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=0@kafka:9093

      - KAFKA_CFG_LISTENERS=SASL_PLAINTEXT://:9092,CONTROLLER://:9093
      - KAFKA_CFG_ADVERTISED_LISTENERS=SASL_PLAINTEXT://kafka:9092
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,SASL_PLAINTEXT:SASL_PLAINTEXT
      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER

      - KAFKA_KRAFT_CLUSTER_ID=f47ac10b-58cc-4372-a567-0e02b2c3d479

      # Enable SASL/PLAIN authentication
      - KAFKA_CFG_SASL_ENABLED_MECHANISMS=PLAIN
      - KAFKA_CFG_SASL_MECHANISM_INTER_BROKER_PROTOCOL=PLAIN
      - KAFKA_CFG_SECURITY_INTER_BROKER_PROTOCOL=SASL_PLAINTEXT

      # Provide credentials
      - KAFKA_CLIENT_USERS=admin
      - KAFKA_CLIENT_PASSWORDS=admin123

      # Bitnami specific settings
      - ALLOW_PLAINTEXT_LISTENER=yes

    volumes:
      - kafka_data:/bitnami/kafka
    networks:
      - hrm-network
    restart: unless-stopped

volumes:
  postgres_data:
  kafka_data:

networks:
  hrm-network:
    driver: bridge
