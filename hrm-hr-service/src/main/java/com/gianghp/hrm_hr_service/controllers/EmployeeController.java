package com.gianghp.hrm_hr_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import com.gianghp.hrm_hr_service.dtos.EmployeeDto;
import com.gianghp.hrm_hr_service.services.EmployeeService;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * Employee Controller - <PERSON><PERSON> dụ về cách sử dụng JWT validation
 */
@RestController
@RequestMapping("/employees")
@RequiredArgsConstructor
public class EmployeeController {

  private final EmployeeService employeeService;

  @GetMapping
  @PreAuthorize("hasRole('ADMIN')")
  public ResponseEntity<ApiResponse<List<EmployeeDto>>> getAllEmployees(
      // <-- Thay đổi kiểu trả về của ResponseEntity
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "10") int size,
      @RequestParam(defaultValue = "createdAt") String sortBy,
      @RequestParam(defaultValue = "desc") String sortDir
  ) {
    try {
      Sort sort = sortDir.equalsIgnoreCase("desc")
          ? Sort.by(sortBy).descending()
          : Sort.by(sortBy).ascending();

      Pageable pageable = PageRequest.of(page, size, sort);

      // Lấy Page<EmployeeDto> từ service
      Page<EmployeeDto> employeePage = employeeService.getAllEmployees(pageable);

      // Tạo ApiResponse với đầy đủ thông tin phân trang
      ApiResponse<List<EmployeeDto>> response = ApiResponse.success(
          "Employees retrieved successfully",
          employeePage.getContent(),             // Dữ liệu thực tế của trang
          employeePage.getTotalElements(),       // Tổng số phần tử
          employeePage.getTotalPages(),          // Tổng số trang
          employeePage.getNumber()               // Số trang hiện tại (pageNumber)
      );

      return ResponseEntity.ok(response);
    } catch (Exception e) {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body(ApiResponse.error("Failed to retrieve employees: " + e.getMessage()));
    }
  }

  @GetMapping("/{id}")
  @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
  public ResponseEntity<ApiResponse<EmployeeDto>> getEmployeeById(@PathVariable UUID id) {
    try {
      EmployeeDto employee = employeeService.getEmployeeById(id);
      return ResponseEntity.ok(ApiResponse.success("Employee retrieved successfully", employee));
    } catch (Exception e) {
      return ResponseEntity.status(
              HttpStatus.NOT_FOUND) // Nên dùng HttpStatus rõ ràng hơn cho "not found"
          .body(ApiResponse.error("Failed to retrieve employee: " + e.getMessage()));
    }
  }

  @GetMapping("/without-department")
  @PreAuthorize("hasRole('ADMIN')")
  public ResponseEntity<ApiResponse<List<EmployeeDto>>> getEmployeesWithoutDepartment(
  ) {
    try {
      List<EmployeeDto> employees = employeeService.getEmployeesWithoutDepartment();

      ApiResponse<List<EmployeeDto>> response = ApiResponse.success(
          "Employees retrieved successfully",
          employees
      );

      return ResponseEntity.ok(response);
    } catch (Exception e) {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body(ApiResponse.error("Failed to retrieve employees: " + e.getMessage()));
    }
  }

  @GetMapping("/me")
  public ResponseEntity<ApiResponse<EmployeeDto>> getMyInfo(
      @Parameter(hidden = true) @RequestHeader("Authorization") String authHeader
  ) {
    try {
      String token = authHeader.substring(7);
      UUID userId = jwtService.extractUserId(token);

      EmployeeDto employee = employeeService.getMyInfo(userId);
      return ResponseEntity.ok(ApiResponse.success("Employee retrieved successfully", employee));

    } catch (Exception e) {
      return ResponseEntity.status(
              HttpStatus.NOT_FOUND) // Nên dùng HttpStatus rõ ràng hơn cho "not found"
          .body(ApiResponse.error("Failed to retrieve employee: " + e.getMessage()));
    }
  }

  @PostMapping
  @PreAuthorize("hasRole('ADMIN')")
  public ResponseEntity<ApiResponse<EmployeeDto>> createEmployee(
      @RequestBody EmployeeDto employeeDto) {
    try {
      EmployeeDto createdEmployee = employeeService.createEmployee(employeeDto);
      return ResponseEntity.status(HttpStatus.CREATED)
          .body(ApiResponse.success("Employee created successfully", createdEmployee));
    } catch (Exception e) {
      return ResponseEntity.status(HttpStatus.BAD_REQUEST)
          .body(ApiResponse.error("Failed to create employee: " + e.getMessage()));
    }
  }

  @PutMapping("/{id}/change-department")
  @PreAuthorize("hasRole('ADMIN')")
  public ResponseEntity<ApiResponse<String>> changeEmployeeDepartment(
      @PathVariable UUID id,
      @RequestBody UUID departmentId) {
    try {
      employeeService.changeEmployeeDepartment(id, departmentId);
      return ResponseEntity.ok(
          ApiResponse.success("Employee department changed successfully", null));
    } catch (Exception e) {
      return ResponseEntity.badRequest()
          .body(ApiResponse.error("Failed to change employee department: " + e.getMessage()));
    }
  }

  @PutMapping("/{id}/change-designation")
  @PreAuthorize("hasRole('ADMIN')")
  public ResponseEntity<ApiResponse<String>> changeEmployeeDesignation(
      @PathVariable UUID id,
      @RequestBody UUID designationId) {
    try {
      employeeService.changeEmployeeDesignation(id, designationId);
      return ResponseEntity.ok(
          ApiResponse.success("Employee designation changed successfully", null));
    } catch (Exception e) {
      return ResponseEntity.badRequest()
          .body(ApiResponse.error("Failed to change employee designation: " + e.getMessage()));
    }
  }
}