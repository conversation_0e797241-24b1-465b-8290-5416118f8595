@echo off

REM Build script for hrm-hr-service Docker image
REM This script should be run from the project root directory

echo Building hrm-hr-service Docker image...

REM Check if we're in the correct directory (project root)
if not exist "settings.gradle" (
    echo Error: This script must be run from the project root directory ^(where settings.gradle is located^)
    exit /b 1
)

REM Build the Docker image from the project root context
docker build -f hrm-hr-service/Dockerfile -t hrm-hr-service:latest .

if %ERRORLEVEL% neq 0 (
    echo Docker build failed!
    exit /b 1
)

echo Docker image built successfully!
echo To run the container:
echo docker run -p 6020:6020 --name hrm-hr-service hrm-hr-service:latest
