#!/bin/bash

# Build script for hrm-hr-service Docker image
# This script should be run from the project root directory

set -e

echo "Building hrm-hr-service Docker image..."

# Check if we're in the correct directory (project root)
if [ ! -f "settings.gradle" ]; then
    echo "Error: This script must be run from the project root directory (where settings.gradle is located)"
    exit 1
fi

# Build the Docker image from the project root context
docker build -f hrm-hr-service/Dockerfile -t hrm-hr-service:latest .

echo "Docker image built successfully!"
echo "To run the container:"
echo "docker run -p 6020:6020 --name hrm-hr-service hrm-hr-service:latest"
