package com.gianghp.hrm_payroll_service.mappers;

import com.gianghp.hrm.dtos.BaseSalaryBasicDto;
import com.gianghp.hrm_payroll_service.dtos.BaseSalaryDto;
import com.gianghp.hrm_payroll_service.dtos.CreateBaseSalaryDto;
import com.gianghp.hrm_payroll_service.entities.BaseSalary;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface BaseSalaryMapper {
    BaseSalaryMapper INSTANCE = Mappers.getMapper(BaseSalaryMapper.class);

    @Mapping(target = "employeeId", expression = "java(baseSalary.getEmployee().getId())")
    @Mapping(target = "employeeCode", expression = "java(baseSalary.getEmployee().getEmployeeCode())")
    BaseSalaryDto toDto(BaseSalary baseSalary);

    @Mapping(target = "employee", ignore = true)
    BaseSalary toEntity(BaseSalaryDto dto);

    @Mapping(target = "employee", ignore = true)
    BaseSalary toEntity(CreateBaseSalaryDto dto);

    @Mapping(target = "employee", ignore = true)
    BaseSalary toEntity(BaseSalaryBasicDto dto);


}
