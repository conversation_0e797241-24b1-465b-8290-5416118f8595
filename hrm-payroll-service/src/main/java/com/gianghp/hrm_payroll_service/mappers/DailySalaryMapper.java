package com.gianghp.hrm_payroll_service.mappers;

import com.gianghp.hrm.enums.CheckinStatus;
import com.gianghp.hrm.enums.CheckoutStatus;
import com.gianghp.hrm_payroll_service.dtos.DailySalaryDto;
import com.gianghp.hrm_payroll_service.entities.DailySalary;
import java.math.BigDecimal;
import java.time.LocalDate;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface DailySalaryMapper {
  DailySalaryMapper INSTANCE = Mappers.getMapper(DailySalaryMapper.class);

  @Mapping(target = "date", expression = "java(dailySalary.getAttendance().getDate())")
  @Mapping(target = "checkinStatus", expression = "java(dailySalary.getAttendance().getCheckinStatus())")
  @Mapping(target = "checkoutStatus", expression = "java(dailySalary.getAttendance().getCheckoutStatus())")
  @Mapping(target = "standardWorkingHours", expression = "java(dailySalary.getAttendance().getStandardWorkingHours())")
  @Mapping(target = "overtimeHours", expression = "java(dailySalary.getAttendance().getOvertimeHours())")
  @Mapping(target = "totalLateDeductionRate", expression = "java(dailySalary.getAttendance().getTotalLateDeductionRate())")
  @Mapping(target = "totalEarlyOutDeductionRate", expression = "java(dailySalary.getAttendance().getTotalEarlyOutDeductionRate())")
  @Mapping(target = "overtimeRate", expression = "java(dailySalary.getAttendance().getOvertimeRate())")
  DailySalaryDto toDto(DailySalary dailySalary);

}
