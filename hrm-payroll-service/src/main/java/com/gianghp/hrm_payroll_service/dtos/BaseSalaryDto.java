package com.gianghp.hrm_payroll_service.dtos;

import com.gianghp.hrm.enums.CurrencyCode;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BaseSalaryDto {
    private UUID id;
    private UUID employeeId;
    private String employeeCode;
    private BigDecimal amount;
    private CurrencyCode currency;
    private LocalDate effectiveDate;
}
