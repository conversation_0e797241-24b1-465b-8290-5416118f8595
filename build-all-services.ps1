Write-Host "Building all HRM services..." -ForegroundColor Green
Write-Host ""

$services = @(
    @{Name="hrm-common"; SkipTests=$false},
    @{Name="hrm-auth-service"; SkipTests=$false},
    @{Name="hrm-hr-service"; SkipTests=$false},
    @{Name="hrm-time-service"; SkipTests=$true},
    @{Name="hrm-payroll-service"; SkipTests=$true}
)

foreach ($service in $services) {
    Write-Host "Building $($service.Name)..." -ForegroundColor Yellow
    
    Set-Location $service.Name
    
    if ($service.SkipTests) {
        $result = & .\gradlew.bat build -x test
    } else {
        $result = & .\gradlew.bat build
    }
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to build $($service.Name)" -ForegroundColor Red
        Set-Location ..
        exit 1
    }
    
    Set-Location ..
    Write-Host "$($service.Name) built successfully!" -ForegroundColor Green
    Write-Host ""
}

Write-Host "All services built successfully!" -ForegroundColor Green
