@echo off
echo Building all HRM services...
echo.

echo Building hrm-common...
cd hrm-common
call gradlew.bat build
if %ERRORLEVEL% neq 0 (
    echo Failed to build hrm-common
    exit /b 1
)
cd ..
echo hrm-common built successfully!
echo.

echo Building hrm-auth-service...
cd hrm-auth-service
call gradlew.bat build
if %ERRORLEVEL% neq 0 (
    echo Failed to build hrm-auth-service
    exit /b 1
)
cd ..
echo hrm-auth-service built successfully!
echo.

echo Building hrm-hr-service...
cd hrm-hr-service
call gradlew.bat build
if %ERRORLEVEL% neq 0 (
    echo Failed to build hrm-hr-service
    exit /b 1
)
cd ..
echo hrm-hr-service built successfully!
echo.

echo Building hrm-time-service...
cd hrm-time-service
call gradlew.bat build -x test
if %ERRORLEVEL% neq 0 (
    echo Failed to build hrm-time-service
    exit /b 1
)
cd ..
echo hrm-time-service built successfully!
echo.

echo Building hrm-payroll-service...
cd hrm-payroll-service
call gradlew.bat build -x test
if %ERRORLEVEL% neq 0 (
    echo Failed to build hrm-payroll-service
    exit /b 1
)
cd ..
echo hrm-payroll-service built successfully!
echo.

echo All services built successfully!
