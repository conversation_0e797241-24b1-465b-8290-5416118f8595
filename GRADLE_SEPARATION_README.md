# HRM Services - Independent Gradle Builds

Dự án HRM đã được tách thành các service độc lập, mỗi service có gradle wrapper riêng.

## Cấu trúc sau khi tách:

```
hrm/
├── hrm-common/                 # Shared library
│   ├── gradle/
│   ├── gradlew
│   ├── gradlew.bat
│   ├── settings.gradle
│   └── build.gradle
├── hrm-auth-service/          # Authentication service
│   ├── gradle/
│   ├── gradlew
│   ├── gradlew.bat
│   ├── settings.gradle
│   └── build.gradle
├── hrm-hr-service/            # HR management service
│   ├── gradle/
│   ├── gradlew
│   ├── gradlew.bat
│   ├── settings.gradle
│   └── build.gradle
├── hrm-time-service/          # Time tracking service
│   ├── gradle/
│   ├── gradlew
│   ├── gradlew.bat
│   ├── settings.gradle
│   └── build.gradle
├── hrm-payroll-service/       # Payroll service
│   ├── gradle/
│   ├── gradlew
│   ├── gradlew.bat
│   ├── settings.gradle
│   └── build.gradle
├── build-all-services.bat     # Build script for Windows
├── build-all-services.ps1     # PowerShell build script
└── settings.gradle            # Root (không còn quản lý multi-project)
```

## Cách build từng service:

### 1. Build hrm-common (phải build trước):
```bash
cd hrm-common
./gradlew build
```

### 2. Build các service khác:
```bash
# HR Service
cd hrm-hr-service
./gradlew build

# Time Service
cd hrm-time-service
./gradlew build -x test  # Skip tests vì có lỗi

# Payroll Service
cd hrm-payroll-service
./gradlew build -x test  # Skip tests vì có lỗi

# Auth Service
cd hrm-auth-service
./gradlew build
```

## Cách build tất cả services:

### Windows Batch:
```cmd
build-all-services.bat
```

### PowerShell:
```powershell
.\build-all-services.ps1
```

## Cách cập nhật hrm-common:

1. **Thay đổi code trong hrm-common**
2. **Build hrm-common:**
   ```bash
   cd hrm-common
   ./gradlew build
   ```
3. **Build lại các service sử dụng hrm-common:**
   ```bash
   cd hrm-hr-service
   ./gradlew build --refresh-dependencies
   ```

## Composite Build:

Các service sử dụng **Composite Build** để reference hrm-common:
- `settings.gradle` của mỗi service có `includeBuild '../hrm-common'`
- `build.gradle` sử dụng `implementation 'com.gianghp:hrm-common'`
- Gradle tự động build hrm-common khi cần thiết

## Lợi ích:

1. **Độc lập**: Mỗi service có thể build và deploy riêng biệt
2. **Tự động cập nhật**: Khi hrm-common thay đổi, các service tự động sử dụng phiên bản mới
3. **Dễ maintain**: Không cần quản lý multi-project phức tạp
4. **CI/CD friendly**: Có thể build từng service riêng trong pipeline

## Lưu ý:

- Luôn build hrm-common trước khi build các service khác lần đầu
- Sử dụng `--refresh-dependencies` khi cần force refresh hrm-common
- Một số test đang bị lỗi nên cần skip với `-x test`
